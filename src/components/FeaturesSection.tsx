import { motion } from "framer-motion";
import FeatureCard from "@/components/FeatureCard";
import { DollarSign, Zap, Users, Headset, BarChart2, LineChart } from "lucide-react";
import { memo } from "react";
import { useCheckout } from "./checkout";

// Memoize the component to prevent unnecessary re-renders
const FeaturesSection = memo(() => {
  const { openCheckout } = useCheckout();
  
  const features = [
    {
      title: "+EV Bets",
      icon: <DollarSign className="w-6 h-6 md:w-7 md:h-7" />,
      description: "Identify bets where the math is in your favor"
    },
    {
      title: "Real Time Alerts",
      icon: <Zap className="w-6 h-6 md:w-7 md:h-7" />,
      description: "Get mobile alerts for profitable opportunities"
    },
    {
      title: "Community",
      icon: <Users className="w-6 h-6 md:w-7 md:h-7" />,
      description: "Learn from successful bettors in our community"
    },
    {
      title: "1 on 1 Support",
      icon: <Headset className="w-6 h-6 md:w-7 md:h-7" />,
      description: "Expert guidance tailored to your betting strategy"
    },
    {
      title: "Odds Comparison",
      icon: <BarChart2 className="w-6 h-6 md:w-7 md:h-7" />,
      description: "Compare odds across major sportsbooks instantly"
    },
    {
      title: "Daily Sharp Bets",
      icon: <LineChart className="w-6 h-6 md:w-7 md:h-7" />,
      description: "Access curated picks from professional bettors"
    },
  ];

  const stats = [
    {
      value: "1,000+",
      label: "Active Members",
    },
    {
      value: "$1M+",
      label: "Generated Profit",
    },
    {
      value: "5-20%",
      label: "Average ROI Gains",
    }
  ];

  // Simplified animation variants for better performance
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        staggerChildren: 0.05,
        delayChildren: 0.1,
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.3 }
    }
  };

  return (
    <section id="features" className="container mx-auto px-3 py-8 sm:py-10 md:py-14 relative">
      <div className="text-center mb-6 sm:mb-8 md:mb-10 relative z-10">
        <h2 className="text-xl sm:text-2xl md:text-4xl font-bold mb-1 sm:mb-2 md:mb-3 heading-gradient">
          Sports Betting Backed by Data
        </h2>
        <p className="text-xs sm:text-sm md:text-lg max-w-3xl mx-auto text-gray-300">
          Datawise takes the emotion out of sports betting. Achieve long term success with a statistical edge.
        </p>
      </div>
      
      {/* Use a single motion.div container with staggered children for better performance */}
      <motion.div 
        className="grid grid-cols-3 gap-2 sm:gap-3 md:gap-4 relative z-10 mb-8"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
        variants={containerVariants}
      >
        {features.map((feature, index) => (
          <motion.div key={index} variants={itemVariants}>
            <FeatureCard 
              title={feature.title} 
              icon={feature.icon} 
              description={feature.description}
              delay={0} // Remove individual delays for better performance
            />
          </motion.div>
        ))}
      </motion.div>

      {/* Stats Banner with optimized animations */}
      <motion.div 
        className="mt-8 bg-black/40 rounded-xl p-4 sm:p-5 backdrop-blur-sm border border-gold/10 relative"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.4 }}
        viewport={{ once: true, amount: 0.1 }}
      >
        <motion.div 
          className="grid grid-cols-3 gap-2 sm:gap-4 mb-4"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          variants={containerVariants}
        >
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              className="text-center"
              variants={itemVariants}
            >
              <div className="text-gold text-lg sm:text-2xl md:text-4xl font-bold mb-0.5 sm:mb-1">
                {stat.value}
              </div>
              <div className="text-white text-xs sm:text-sm md:text-base font-semibold">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>

        <div className="text-gray-500 text-[10px] text-center italic mb-3">
          *Results vary based on bankroll size, bet selection, and market conditions. Past performance does not guarantee future results.
        </div>

        <div className="text-center">
          <button
            onClick={openCheckout}
            className="bg-gold hover:bg-gold/90 text-black font-bold py-2 px-6 rounded-full text-sm md:text-base transition-all duration-300"
          >
            Start Your 7-Day Free Trial
          </button>
        </div>
      </motion.div>
    </section>
  );
});

// Add display name for debugging
FeaturesSection.displayName = "FeaturesSection";

export default FeaturesSection;
