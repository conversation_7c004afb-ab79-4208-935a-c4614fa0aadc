import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import { CheckoutModal } from './CheckoutModal';

interface CheckoutContextType {
  openCheckout: (planId?: string) => void;
  closeCheckout: () => void;
  isOpen: boolean;
  preloadCheckout: () => void;
}

const CheckoutContext = createContext<CheckoutContextType | undefined>(undefined);

export const useCheckout = () => {
  const context = useContext(CheckoutContext);
  if (!context) {
    // Return a no-op implementation when used outside of provider
    // This can happen during lazy loading or SSR
    console.warn('useCheckout used outside of CheckoutProvider');
    return {
      openCheckout: () => console.warn('Checkout not available - component may be rendering outside CheckoutProvider'),
      closeCheckout: () => {},
      isOpen: false,
      preloadCheckout: () => {}
    };
  }
  return context;
};

interface CheckoutProviderProps {
  children: React.ReactNode;
  defaultPlanId?: string;
}

export const CheckoutProvider: React.FC<CheckoutProviderProps> = ({ 
  children,
  defaultPlanId = "plan_NCr6hVh2qtYBb" // Replace with your actual default plan ID
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [planId, setPlanId] = useState(defaultPlanId);
  const [isPreloaded, setIsPreloaded] = useState(false);
  const preloadFrameRef = useRef<HTMLIFrameElement | null>(null);

  // Preload checkout on mount for instant loading
  useEffect(() => {
    const preloadFrame = document.createElement('iframe');
    preloadFrame.src = `/checkout.html?planId=${defaultPlanId}&theme=dark&preload=true`;
    preloadFrame.style.position = 'absolute';
    preloadFrame.style.width = '1px';
    preloadFrame.style.height = '1px';
    preloadFrame.style.opacity = '0';
    preloadFrame.style.pointerEvents = 'none';
    preloadFrame.onload = () => {
      setIsPreloaded(true);
    };
    document.body.appendChild(preloadFrame);
    preloadFrameRef.current = preloadFrame;

    return () => {
      if (preloadFrameRef.current && document.body.contains(preloadFrameRef.current)) {
        document.body.removeChild(preloadFrameRef.current);
      }
    };
  }, [defaultPlanId]);

  const preloadCheckout = () => {
    // Already preloading on mount
  };

  const openCheckout = (newPlanId?: string) => {
    if (newPlanId) {
      setPlanId(newPlanId);
    }
    setIsOpen(true);
  };

  const closeCheckout = () => {
    setIsOpen(false);
  };

  return (
    <CheckoutContext.Provider value={{ openCheckout, closeCheckout, isOpen, preloadCheckout }}>
      {children}
      <CheckoutModal 
        isOpen={isOpen}
        onOpenChange={setIsOpen}
        planId={planId}
        theme="dark"
      />
    </CheckoutContext.Provider>
  );
};