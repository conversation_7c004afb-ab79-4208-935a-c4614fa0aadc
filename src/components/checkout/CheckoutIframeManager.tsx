import React, { useEffect, useRef, useState } from 'react';

interface CheckoutIframeManagerProps {
  planId: string;
  theme: string;
  onReady: () => void;
}

// This component manages a persistent background iframe that's always loaded
export const CheckoutIframeManager: React.FC<CheckoutIframeManagerProps> = ({
  planId,
  theme,
  onReady
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    if (!containerRef.current) return;

    // Create the iframe immediately
    const iframe = document.createElement('iframe');
    iframe.src = `/checkout.html?planId=${planId}&theme=${theme}&persistent=true`;
    iframe.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      max-width: 900px;
      height: 750px;
      border: none;
      opacity: 0;
      pointer-events: none;
      z-index: -1;
      transition: opacity 0.2s ease-in-out;
    `;
    
    // Mark the iframe with a special class for identification
    iframe.className = 'checkout-iframe-pool';
    iframe.setAttribute('data-ready', 'false');
    
    iframe.onload = () => {
      // Give Whop time to initialize
      setTimeout(() => {
        iframe.setAttribute('data-ready', 'true');
        setIsReady(true);
        onReady();
      }, 300);
    };

    containerRef.current.appendChild(iframe);

    // Listen for Whop ready message
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'whop-ready' && event.source === iframe.contentWindow) {
        iframe.setAttribute('data-ready', 'true');
        setIsReady(true);
        onReady();
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
      iframe.remove();
    };
  }, [planId, theme, onReady]);

  return <div ref={containerRef} style={{ display: 'none' }} />;
};

// Hook to manage the iframe pool
export const useCheckoutIframe = () => {
  const [isReady, setIsReady] = useState(false);
  
  useEffect(() => {
    // Check if iframe is ready periodically
    const checkReady = setInterval(() => {
      const iframe = document.querySelector('.checkout-iframe-pool') as HTMLIFrameElement;
      if (iframe && iframe.getAttribute('data-ready') === 'true') {
        setIsReady(true);
        clearInterval(checkReady);
      }
    }, 100);
    
    return () => clearInterval(checkReady);
  }, []);
  
  const showIframe = () => {
    const iframe = document.querySelector('.checkout-iframe-pool') as HTMLIFrameElement;
    if (iframe && iframe.getAttribute('data-ready') === 'true') {
      // Position it inside the modal
      const modalContent = document.getElementById('checkout-iframe-container');
      if (modalContent) {
        modalContent.appendChild(iframe);
        iframe.style.position = 'absolute';
        iframe.style.top = '0';
        iframe.style.left = '0';
        iframe.style.transform = 'none';
        iframe.style.width = '100%';
        iframe.style.maxWidth = '100%';
        iframe.style.height = '100%';
        iframe.style.opacity = '1';
        iframe.style.pointerEvents = 'auto';
        iframe.style.zIndex = '1';
      }
      return true;
    }
    return false;
  };

  const hideIframe = () => {
    const iframe = document.querySelector('.checkout-iframe-pool') as HTMLIFrameElement;
    if (iframe) {
      // Move it back to body and hide
      document.body.appendChild(iframe);
      iframe.style.position = 'fixed';
      iframe.style.top = '50%';
      iframe.style.left = '50%';
      iframe.style.transform = 'translate(-50%, -50%)';
      iframe.style.width = '100%';
      iframe.style.maxWidth = '900px';
      iframe.style.height = '750px';
      iframe.style.opacity = '0';
      iframe.style.pointerEvents = 'none';
      iframe.style.zIndex = '-1';
    }
  };

  const isIframeReady = () => {
    const iframe = document.querySelector('.checkout-iframe-pool') as HTMLIFrameElement;
    return iframe && iframe.getAttribute('data-ready') === 'true';
  };

  return {
    showIframe,
    hideIframe,
    isReady
  };
};