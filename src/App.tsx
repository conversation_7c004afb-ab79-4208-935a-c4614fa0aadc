import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate, useLocation } from "react-router-dom";
import { SpeedInsights } from "@vercel/speed-insights/react";
import { Analytics } from "@vercel/analytics/react";
import { Suspense, useState } from "react";
import { HelmetProvider } from 'react-helmet-async';
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import ScrollToTop from "./components/utils/ScrollToTop";
import ResourcePreloader from "./components/performance/ResourcePreloader";
import PerformanceMonitor from "./components/performance/PerformanceMonitor";
import { AsyncErrorBoundary } from "./components/error/ErrorBoundary";
import { AccessibilityProvider, SkipLink } from "./components/accessibility/AccessibilityProvider";
import { HomepageStructuredData } from "./components/seo/StructuredData";
import { CheckoutProvider } from "./components/checkout";
import { CheckoutIframeManager } from "./components/checkout/CheckoutIframeManager";
// Import testing utilities in development
if (process.env.NODE_ENV === 'development') {
  import('./utils/testResourcePreloading');
}

// Lazy load non-critical routes using standardized system
import { createLazyComponents } from "./components/utils/LazyLoader";

const { GuideDetail, Blog, BettingSimulator } = createLazyComponents({
  GuideDetail: () => import("./pages/GuideDetail"),
  Blog: () => import("./pages/Blog"),
  BettingSimulator: () => import("./pages/BettingSimulator"),
}, {
  preloadMargin: '300px', // Preload routes when user is close to navigating
  minHeight: '100vh', // Full height for route components
});

// Lazy load analytics to reduce initial bundle size
const AnalyticsProvider = createLazyComponents({
  AnalyticsProvider: () => import("./components/analytics/AnalyticsProvider"),
}, {
  preloadMargin: '0px', // Load immediately when component mounts
  minHeight: '0px', // No visual component
}).AnalyticsProvider;

// Configure QueryClient with performance optimizations
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      staleTime: 60000, // 1 minute
    },
  },
});

const App = () => {
  const [checkoutReady, setCheckoutReady] = useState(false);
  
  return (
    <HelmetProvider>
      <AsyncErrorBoundary level="page">
        <QueryClientProvider client={queryClient}>
          <AccessibilityProvider>
            <TooltipProvider>
              <HomepageStructuredData />
              <SkipLink href="#main-content">Skip to main content</SkipLink>
              <Toaster />
              <Sonner />
              <SpeedInsights />
              <Analytics />
              <BrowserRouter>
                <CheckoutProvider>
                  {/* Preload checkout iframe immediately */}
                  <CheckoutIframeManager 
                    planId="plan_NCr6hVh2qtYBb"
                    theme="dark"
                    onReady={() => setCheckoutReady(true)}
                  />
                  <ResourcePreloader
                    enablePerformanceMonitoring={true}
                    enableRoutePreloading={true}
                    enableThirdPartyPreloading={true}
                  />
                  <Suspense fallback={null}>
                    <AnalyticsProvider />
                  </Suspense>
                  <ScrollToTop />
                  {process.env.NODE_ENV === 'development' && (
                    <PerformanceMonitor
                      enableLogging={true}
                      enableAnalytics={false}
                      showDevOverlay={true}
                    />
                  )}
                  <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/blog" element={
              <Suspense fallback={<div className="min-h-screen flex items-center justify-center">Loading...</div>}>
                <Blog />
              </Suspense>
            } />
            <Route path="/betting-simulator" element={
              <Suspense fallback={<div className="min-h-screen flex items-center justify-center">Loading...</div>}>
                <BettingSimulator />
              </Suspense>
            } />
            {/* Redirect /guides to /blog for SEO continuity */}
            <Route path="/guides" element={<Navigate to="/blog" replace />} />
            
            {/* Redirect /guides/:guideId to /blog/:guideId for SEO continuity */}
            <Route path="/guides/:guideId" element={<GuideRedirect />} />
            
            {/* Blog detail pages */}
            <Route path="/blog/:postId" element={
              <Suspense fallback={<div className="min-h-screen flex items-center justify-center">Loading...</div>}>
                <GuideDetail />
              </Suspense>
            } />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </CheckoutProvider>
      </BrowserRouter>
      </TooltipProvider>
    </AccessibilityProvider>
    </QueryClientProvider>
    </AsyncErrorBoundary>
    </HelmetProvider>
  );
};

// Helper component for redirecting from guides to blog with the same slug
const GuideRedirect = () => {
  const location = useLocation();
  const slug = location.pathname.split('/').pop();
  return <Navigate to={`/blog/${slug}`} replace />;
};

export default App;
