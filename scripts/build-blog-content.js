/**
 * Build script to process markdown blog posts at build time
 * This eliminates the need for client-side markdown parsing
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import matter from 'gray-matter';
import { remark } from 'remark';
import html from 'remark-html';
import gfm from 'remark-gfm';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const CONTENT_DIR = path.join(__dirname, '../src/content/blog');
const OUTPUT_FILE = path.join(__dirname, '../public/blog-content.json');

/**
 * Process a single markdown file
 */
async function processMarkdownFile(filePath) {
  try {
    const content = await fs.readFile(filePath, 'utf-8');
    const { data: frontmatter, content: markdownContent } = matter(content);
    
    // Process markdown to HTML
    const processedContent = await remark()
      .use(gfm)
      .use(html, { sanitize: true })
      .process(markdownContent);
    
    const htmlContent = processedContent.toString();
    
    // Extract slug from filename
    const filename = path.basename(filePath);
    const slug = filename.replace('.md', '');
    
    return {
      slug,
      frontmatter,
      content: htmlContent,
      excerpt: frontmatter.excerpt || markdownContent.slice(0, 200).replace(/[#*`]/g, '').trim() + '...'
    };
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error);
    return null;
  }
}

/**
 * Enhance HTML content with styling classes
 */
function enhanceHtmlContent(html) {
  // Add classes to headings
  html = html.replace(/<h1>/g, '<h1 class="text-4xl font-bold mb-6">');
  html = html.replace(/<h2>/g, '<h2 class="text-3xl font-semibold mb-4 mt-8">');
  html = html.replace(/<h3>/g, '<h3 class="text-2xl font-semibold mb-3 mt-6">');
  html = html.replace(/<h4>/g, '<h4 class="text-xl font-semibold mb-2 mt-4">');
  
  // Add classes to paragraphs
  html = html.replace(/<p>/g, '<p class="mb-4 leading-relaxed">');
  
  // Add classes to lists
  html = html.replace(/<ul>/g, '<ul class="list-disc list-inside mb-4 space-y-2">');
  html = html.replace(/<ol>/g, '<ol class="list-decimal list-inside mb-4 space-y-2">');
  
  // Add classes to blockquotes
  html = html.replace(/<blockquote>/g, '<blockquote class="border-l-4 border-primary pl-4 italic my-4">');
  
  // Add classes to code blocks
  html = html.replace(/<pre>/g, '<pre class="bg-muted p-4 rounded-lg overflow-x-auto mb-4">');
  html = html.replace(/<code>/g, '<code class="bg-muted px-1 py-0.5 rounded text-sm">');
  
  // Fix code blocks that are inside pre tags
  html = html.replace(/<pre class="bg-muted p-4 rounded-lg overflow-x-auto mb-4"><code class="bg-muted px-1 py-0.5 rounded text-sm">/g, 
    '<pre class="bg-muted p-4 rounded-lg overflow-x-auto mb-4"><code>');
  
  return html;
}

/**
 * Main build function
 */
async function buildBlogContent() {
  console.log('Building blog content...');
  
  try {
    // Ensure output directory exists
    const outputDir = path.dirname(OUTPUT_FILE);
    await fs.mkdir(outputDir, { recursive: true });
    
    // Read all markdown files
    const files = await fs.readdir(CONTENT_DIR);
    const markdownFiles = files.filter(file => file.endsWith('.md'));
    
    console.log(`Found ${markdownFiles.length} markdown files`);
    
    // Process all files
    const posts = await Promise.all(
      markdownFiles.map(file => 
        processMarkdownFile(path.join(CONTENT_DIR, file))
      )
    );
    
    // Filter out any failed processes and enhance HTML
    const validPosts = posts
      .filter(post => post !== null)
      .map(post => ({
        ...post,
        content: enhanceHtmlContent(post.content)
      }))
      .sort((a, b) => {
        // Sort by date, newest first
        const dateA = new Date(a.frontmatter.date || 0);
        const dateB = new Date(b.frontmatter.date || 0);
        
        // Validate dates and warn if invalid
        if (isNaN(dateA.getTime())) {
          console.warn(`Invalid date for post "${a.slug}": ${a.frontmatter.date}`);
        }
        if (isNaN(dateB.getTime())) {
          console.warn(`Invalid date for post "${b.slug}": ${b.frontmatter.date}`);
        }
        
        return dateB - dateA;
      });
    
    console.log(`Successfully processed ${validPosts.length} posts`);
    
    // Write to output file
    await fs.writeFile(
      OUTPUT_FILE,
      JSON.stringify(validPosts, null, 2),
      'utf-8'
    );
    
    console.log(`Blog content written to ${OUTPUT_FILE}`);
    
    // Also create a lightweight index for faster initial loads
    const index = validPosts.map(({ slug, frontmatter, excerpt }) => ({
      slug,
      title: frontmatter.title,
      date: frontmatter.date,
      excerpt,
      image: frontmatter.image,
      categories: frontmatter.categories,
      author: frontmatter.author,
      readTime: frontmatter.readTime,
      featured: frontmatter.featured
    }));
    
    await fs.writeFile(
      path.join(outputDir, 'blog-index.json'),
      JSON.stringify(index, null, 2),
      'utf-8'
    );
    
    console.log('Blog index created');
    
  } catch (error) {
    console.error('Error building blog content:', error);
    process.exit(1);
  }
}

// Run the build
buildBlogContent();