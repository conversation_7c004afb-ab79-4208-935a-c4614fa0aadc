/**
 * Service Worker for Datawise Bets
 * Implements aggressive caching strategies for optimal performance
 */

const CACHE_NAME = 'datawise-bets-v1';
const STATIC_CACHE = 'datawise-static-v1';
const DYNAMIC_CACHE = 'datawise-dynamic-v1';
const API_CACHE = 'datawise-api-v1';

// Cache duration constants (in milliseconds)
const CACHE_DURATIONS = {
  STATIC: 7 * 24 * 60 * 60 * 1000, // 7 days
  DYNAMIC: 24 * 60 * 60 * 1000,    // 1 day
  API: 5 * 60 * 1000,              // 5 minutes
  IMAGES: 30 * 24 * 60 * 60 * 1000 // 30 days
};

// Static assets to cache immediately
// Note: Fonts are now loaded from Google Fonts CDN
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon/favicon.ico',
  '/blog-index.json',
  '/blog-content.json'
];

// Routes to cache with stale-while-revalidate
const DYNAMIC_ROUTES = [
  '/blog',
  '/guides',
  '/betting-simulator'
];

// API endpoints to cache
const API_PATTERNS = [
  /\/api\/blog/,
  /\/api\/guides/,
  /\/api\/analytics/
];

/**
 * Install event - cache static assets
 */
self.addEventListener('install', (event) => {
  console.log('[SW] Installing service worker...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('[SW] Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('[SW] Static assets cached successfully');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('[SW] Failed to cache static assets:', error);
      })
  );
});

/**
 * Activate event - clean up old caches
 */
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating service worker...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && 
                cacheName !== DYNAMIC_CACHE && 
                cacheName !== API_CACHE) {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('[SW] Service worker activated');
        return self.clients.claim();
      })
  );
});

/**
 * Fetch event - implement caching strategies
 */
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return;
  }

  event.respondWith(handleRequest(request));
});

/**
 * Handle different types of requests with appropriate caching strategies
 */
async function handleRequest(request) {
  const url = new URL(request.url);
  
  try {
    // Static assets - Cache First
    if (isStaticAsset(url)) {
      return await cacheFirst(request, STATIC_CACHE);
    }
    
    // API requests - Stale While Revalidate
    if (isApiRequest(url)) {
      return await staleWhileRevalidate(request, API_CACHE);
    }
    
    // Images - Cache First with fallback
    if (isImageRequest(url)) {
      return await cacheFirst(request, STATIC_CACHE);
    }
    
    // Dynamic routes - Stale While Revalidate
    if (isDynamicRoute(url)) {
      return await staleWhileRevalidate(request, DYNAMIC_CACHE);
    }
    
    // Default - Network First
    return await networkFirst(request, DYNAMIC_CACHE);
    
  } catch (error) {
    console.error('[SW] Request failed:', error);
    return await handleOffline(request);
  }
}

/**
 * Cache First strategy - for static assets
 */
async function cacheFirst(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse && !isExpired(cachedResponse)) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    if (cachedResponse) {
      return cachedResponse;
    }
    throw error;
  }
}

/**
 * Stale While Revalidate strategy - for dynamic content
 */
async function staleWhileRevalidate(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  // Always try to fetch fresh content in background
  const fetchPromise = fetch(request)
    .then((networkResponse) => {
      if (networkResponse.ok) {
        cache.put(request, networkResponse.clone());
      }
      return networkResponse;
    })
    .catch(() => null);
  
  // Return cached version immediately if available
  if (cachedResponse) {
    return cachedResponse;
  }
  
  // If no cache, wait for network
  return await fetchPromise || handleOffline(request);
}

/**
 * Network First strategy - for critical dynamic content
 */
async function networkFirst(request, cacheName) {
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    return handleOffline(request);
  }
}

/**
 * Handle offline scenarios
 */
async function handleOffline(request) {
  const url = new URL(request.url);
  
  // Return cached index.html for navigation requests
  if (request.mode === 'navigate') {
    const cache = await caches.open(STATIC_CACHE);
    return await cache.match('/index.html') || new Response('Offline', { status: 503 });
  }
  
  return new Response('Offline', { status: 503 });
}

/**
 * Utility functions for request classification
 */
function isStaticAsset(url) {
  return url.pathname.match(/\.(js|css|woff2?|ttf|eot|ico|svg)$/) ||
         STATIC_ASSETS.some(asset => url.pathname === asset);
}

function isApiRequest(url) {
  return API_PATTERNS.some(pattern => pattern.test(url.pathname)) ||
         url.pathname.startsWith('/api/');
}

function isImageRequest(url) {
  return url.pathname.match(/\.(png|jpg|jpeg|webp|avif|gif)$/);
}

function isDynamicRoute(url) {
  return DYNAMIC_ROUTES.some(route => url.pathname.startsWith(route));
}

function isExpired(response) {
  const dateHeader = response.headers.get('date');
  if (!dateHeader) return false;
  
  const responseDate = new Date(dateHeader);
  const now = new Date();
  const age = now.getTime() - responseDate.getTime();
  
  // Determine cache duration based on content type
  let maxAge = CACHE_DURATIONS.DYNAMIC;
  
  const contentType = response.headers.get('content-type') || '';
  if (contentType.includes('image/')) {
    maxAge = CACHE_DURATIONS.IMAGES;
  } else if (response.url.includes('/api/')) {
    maxAge = CACHE_DURATIONS.API;
  }
  
  return age > maxAge;
}

/**
 * Background sync for analytics and non-critical data
 */
self.addEventListener('sync', (event) => {
  if (event.tag === 'analytics-sync') {
    event.waitUntil(syncAnalytics());
  }
});

async function syncAnalytics() {
  // Implement analytics sync when back online
  console.log('[SW] Syncing analytics data...');
}

/**
 * Push notification handling (for future use)
 */
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json();
    event.waitUntil(
      self.registration.showNotification(data.title, {
        body: data.body,
        icon: '/favicon/favicon-96x96.png',
        badge: '/favicon/favicon-96x96.png'
      })
    );
  }
});
